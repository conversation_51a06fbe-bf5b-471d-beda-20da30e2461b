use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::Notify;
use flume::{Receiver, Sender};
use arien_common::{Result, ArienError, SubmissionId, SessionId};
use crate::{Submission, Event, Session, Config};

/// Main Arien interface - operates as a queue pair where clients send submissions and receive events
pub struct Arien {
    next_id: AtomicU64,
    tx_sub: Sender<Submission>,
    rx_event: Receiver<Event>,
    ctrl_c: Arc<Notify>,
}

impl Arien {
    /// Create a new Arien instance with the given configuration
    pub async fn new(config: Config) -> Result<Self> {
        let (tx_sub, rx_sub) = flume::unbounded();
        let (tx_event, rx_event) = flume::unbounded();
        let ctrl_c = Arc::new(Notify::new());

        let arien = Self {
            next_id: AtomicU64::new(1),
            tx_sub,
            rx_event,
            ctrl_c: ctrl_c.clone(),
        };

        // Start the session processor in the background
        let session_processor = SessionProcessor::new(rx_sub, tx_event, ctrl_c, config);
        tokio::spawn(async move {
            if let Err(e) = session_processor.run().await {
                tracing::error!("Session processor error: {}", e);
            }
        });

        Ok(arien)
    }

    /// Submit an operation and get a submission ID
    pub async fn submit(&self, session_id: SessionId, op: crate::Op) -> Result<SubmissionId> {
        let id = SubmissionId::new(self.next_id.fetch_add(1, Ordering::SeqCst));
        let submission = Submission::new(id, session_id, op);
        
        self.tx_sub.send_async(submission).await
            .map_err(|_| ArienError::InternalAgentDied)?;
        
        Ok(id)
    }

    /// Receive the next event
    pub async fn recv_event(&self) -> Result<Event> {
        self.rx_event.recv_async().await
            .map_err(|_| ArienError::InternalAgentDied)
    }

    /// Try to receive an event without blocking
    pub fn try_recv_event(&self) -> Result<Option<Event>> {
        match self.rx_event.try_recv() {
            Ok(event) => Ok(Some(event)),
            Err(flume::TryRecvError::Empty) => Ok(None),
            Err(flume::TryRecvError::Disconnected) => Err(ArienError::InternalAgentDied),
        }
    }

    /// Signal interrupt (Ctrl+C)
    pub fn interrupt(&self) {
        self.ctrl_c.notify_waiters();
    }

    /// Get the next submission ID that would be assigned
    pub fn next_submission_id(&self) -> SubmissionId {
        SubmissionId::new(self.next_id.load(Ordering::SeqCst))
    }
}

/// Internal session processor that handles submissions and generates events
struct SessionProcessor {
    rx_sub: Receiver<Submission>,
    tx_event: Sender<Event>,
    ctrl_c: Arc<Notify>,
    config: Config,
    sessions: std::collections::HashMap<SessionId, Session>,
}

impl SessionProcessor {
    fn new(
        rx_sub: Receiver<Submission>,
        tx_event: Sender<Event>,
        ctrl_c: Arc<Notify>,
        config: Config,
    ) -> Self {
        Self {
            rx_sub,
            tx_event,
            ctrl_c,
            config,
            sessions: std::collections::HashMap::new(),
        }
    }

    async fn run(mut self) -> Result<()> {
        loop {
            tokio::select! {
                submission = self.rx_sub.recv_async() => {
                    match submission {
                        Ok(sub) => {
                            if let Err(e) = self.handle_submission(sub).await {
                                tracing::error!("Error handling submission: {}", e);
                            }
                        }
                        Err(_) => {
                            tracing::info!("Submission channel closed, shutting down");
                            break;
                        }
                    }
                }
                _ = self.ctrl_c.notified() => {
                    tracing::info!("Received interrupt signal, shutting down");
                    break;
                }
            }
        }

        Ok(())
    }

    async fn handle_submission(&mut self, submission: Submission) -> Result<()> {
        let session_id = submission.session_id.clone();

        // Get or create session
        if !self.sessions.contains_key(&session_id) {
            let session = Session::new(
                session_id.clone(),
                self.tx_event.clone(),
                self.ctrl_c.clone(),
                self.config.clone(),
            ).await?;
            self.sessions.insert(session_id.clone(), session);
        }

        let session = self.sessions.get_mut(&session_id)
            .ok_or_else(|| ArienError::unknown("Session not found after creation"))?;

        session.handle_submission(submission).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{Op, SessionConfig};

    #[tokio::test]
    async fn test_arien_creation() {
        let config = Config::default();
        let arien = Arien::new(config).await.unwrap();
        
        // Test that we can get a submission ID
        let next_id = arien.next_submission_id();
        assert_eq!(next_id.0, 1);
    }

    #[tokio::test]
    async fn test_submission_and_event() {
        let config = Config::default();
        let arien = Arien::new(config).await.unwrap();
        
        let session_id = SessionId::new();
        let op = Op::ConfigureSession {
            config: SessionConfig {
                model: Some("test-model".to_string()),
                model_provider: None,
                instructions: None,
                approval_policy: None,
                sandbox_policy: None,
                shell_environment_policy: None,
                writable_roots: None,
                mcp_servers: None,
            },
        };

        // Submit operation
        let submission_id = arien.submit(session_id, op).await.unwrap();
        assert_eq!(submission_id.0, 1);

        // Should receive a session configured event
        let event = arien.recv_event().await.unwrap();
        assert_eq!(event.submission_id, submission_id);
        assert_eq!(event.session_id, session_id);
        
        match event.event_type {
            crate::EventType::SessionConfigured { session_id: event_session_id } => {
                assert_eq!(event_session_id, session_id);
            }
            _ => panic!("Expected SessionConfigured event"),
        }
    }
}
