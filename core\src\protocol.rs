use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use arien_common::{SubmissionId, SessionId, ToolCallId, Timestamp, SafePath, ContentType};

/// Submission from user to agent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Submission {
    pub id: SubmissionId,
    pub session_id: SessionId,
    pub op: Op,
    pub timestamp: Timestamp,
}

/// Event from agent to user
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Event {
    pub submission_id: SubmissionId,
    pub session_id: SessionId,
    pub event_type: EventType,
    pub timestamp: Timestamp,
}

/// Operation types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum Op {
    ConfigureSession {
        config: SessionConfig,
    },
    UserInput {
        items: Vec<InputItem>,
    },
    ExecApproval {
        approved: bool,
        command: Vec<String>,
    },
    PatchApproval {
        approved: bool,
        patch_id: String,
    },
    ToolApproval {
        approved: bool,
        tool_call_id: ToolCallId,
    },
    Interrupt,
    Reset,
}

/// Event types
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum EventType {
    SessionConfigured {
        session_id: SessionId,
    },
    ConversationStarted,
    ModelResponse {
        content: String,
        partial: bool,
    },
    ToolCall {
        tool_call_id: ToolCallId,
        tool_name: String,
        arguments: HashMap<String, serde_json::Value>,
    },
    ToolResult {
        tool_call_id: ToolCallId,
        result: ToolResult,
    },
    ExecRequest {
        command: Vec<String>,
        working_dir: Option<SafePath>,
        requires_approval: bool,
    },
    ExecStarted {
        command: Vec<String>,
        working_dir: SafePath,
    },
    ExecOutput {
        output: String,
        is_stderr: bool,
    },
    ExecCompleted {
        exit_code: i32,
        duration_ms: u64,
    },
    PatchRequest {
        patch_id: String,
        description: String,
        changes: Vec<FileChange>,
        requires_approval: bool,
    },
    PatchApplied {
        patch_id: String,
        results: Vec<PatchResult>,
    },
    Error {
        error: String,
        recoverable: bool,
    },
    Warning {
        message: String,
    },
    Info {
        message: String,
    },
    ConversationEnded,
}

/// User input item
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum InputItem {
    Text {
        content: String,
    },
    Image {
        data: String, // Base64 encoded
        mime_type: String,
    },
    LocalImage {
        path: SafePath,
    },
    File {
        path: SafePath,
        content_type: ContentType,
    },
}

/// Session configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionConfig {
    pub model: Option<String>,
    pub model_provider: Option<String>,
    pub instructions: Option<String>,
    pub approval_policy: Option<AskForApproval>,
    pub sandbox_policy: Option<SandboxPolicy>,
    pub shell_environment_policy: Option<ShellEnvironmentPolicy>,
    pub writable_roots: Option<Vec<SafePath>>,
    pub mcp_servers: Option<HashMap<String, McpServerConfig>>,
}

/// Approval policy
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AskForApproval {
    Never,           // Non-interactive mode
    OnFailure,       // Auto-approve with sandbox, escalate on failure
    UnlessAllowListed, // Ask unless pre-approved
    AutoEdit,        // Auto-approve file edits
}

/// Sandbox policy
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SandboxPolicy {
    pub sandbox_type: SandboxType,
    pub permissions: Vec<SandboxPermission>,
}

/// Sandbox type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SandboxType {
    None,
    MacosSeatbelt,
    LinuxSeccomp,
}

/// Sandbox permission
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SandboxPermission {
    FileRead { path: SafePath },
    FileWrite { path: SafePath },
    NetworkAccess,
    ProcessSpawn,
    SystemInfo,
}

/// Shell environment policy
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ShellEnvironmentPolicy {
    pub inherit_env: bool,
    pub allowed_env_vars: Option<Vec<String>>,
    pub blocked_env_vars: Option<Vec<String>>,
    pub custom_env: HashMap<String, String>,
}

/// MCP server configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct McpServerConfig {
    pub command: String,
    pub args: Vec<String>,
    pub env: Option<HashMap<String, String>>,
    pub timeout_seconds: Option<u64>,
}

/// Tool result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    pub success: bool,
    pub content: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// File change for patches
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileChange {
    pub path: SafePath,
    pub change_type: FileChangeType,
    pub content: Option<String>,
    pub old_content: Option<String>,
}

/// File change type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileChangeType {
    Create,
    Update,
    Delete,
    Move { new_path: SafePath },
}

/// Patch application result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatchResult {
    pub path: SafePath,
    pub success: bool,
    pub error: Option<String>,
}

impl Submission {
    pub fn new(id: SubmissionId, session_id: SessionId, op: Op) -> Self {
        Self {
            id,
            session_id,
            op,
            timestamp: Timestamp::now(),
        }
    }
}

impl Event {
    pub fn new(submission_id: SubmissionId, session_id: SessionId, event_type: EventType) -> Self {
        Self {
            submission_id,
            session_id,
            event_type,
            timestamp: Timestamp::now(),
        }
    }
}

impl Default for AskForApproval {
    fn default() -> Self {
        Self::UnlessAllowListed
    }
}

impl Default for SandboxType {
    fn default() -> Self {
        #[cfg(target_os = "macos")]
        return Self::MacosSeatbelt;
        
        #[cfg(target_os = "linux")]
        return Self::LinuxSeccomp;
        
        #[cfg(not(any(target_os = "macos", target_os = "linux")))]
        return Self::None;
    }
}

impl Default for SandboxPolicy {
    fn default() -> Self {
        Self {
            sandbox_type: SandboxType::default(),
            permissions: vec![
                SandboxPermission::NetworkAccess,
                SandboxPermission::SystemInfo,
            ],
        }
    }
}

impl Default for ShellEnvironmentPolicy {
    fn default() -> Self {
        Self {
            inherit_env: true,
            allowed_env_vars: None,
            blocked_env_vars: Some(vec![
                "AWS_ACCESS_KEY_ID".to_string(),
                "AWS_SECRET_ACCESS_KEY".to_string(),
                "GITHUB_TOKEN".to_string(),
                "OPENAI_API_KEY".to_string(),
            ]),
            custom_env: HashMap::new(),
        }
    }
}

impl ToolResult {
    pub fn success(content: impl Into<String>) -> Self {
        Self {
            success: true,
            content: content.into(),
            metadata: None,
        }
    }

    pub fn error(content: impl Into<String>) -> Self {
        Self {
            success: false,
            content: content.into(),
            metadata: None,
        }
    }

    pub fn with_metadata(mut self, metadata: HashMap<String, serde_json::Value>) -> Self {
        self.metadata = Some(metadata);
        self
    }
}
