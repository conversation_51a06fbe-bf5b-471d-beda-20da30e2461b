use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt;

/// ANSI escape sequence handling for terminal output processing
pub mod parser;
pub mod renderer;
pub mod colors;
pub mod cursor;

pub use parser::*;
pub use renderer::*;
pub use colors::*;
pub use cursor::*;

/// ANSI escape sequence types
#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum AnsiSequence {
    /// Reset all formatting
    Reset,
    /// Text color
    ForegroundColor(Color),
    /// Background color
    BackgroundColor(Color),
    /// Text style
    Style(TextStyle),
    /// Cursor movement
    CursorMove(CursorMove),
    /// Clear screen/line
    Clear(ClearType),
    /// Unknown or unsupported sequence
    Unknown(String),
}

/// Text styles
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TextStyle {
    Bold,
    Dim,
    Italic,
    Underline,
    Blink,
    Reverse,
    Strikethrough,
    <PERSON><PERSON><PERSON>,
    <PERSON>m<PERSON><PERSON>,
    Itali<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>erseOff,
    StrikethroughOff,
}

/// Clear types
#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub enum ClearType {
    /// Clear from cursor to end of screen
    ToEndOfScreen,
    /// Clear from cursor to beginning of screen
    ToBeginningOfScreen,
    /// Clear entire screen
    EntireScreen,
    /// Clear from cursor to end of line
    ToEndOfLine,
    /// Clear from cursor to beginning of line
    ToBeginningOfLine,
    /// Clear entire line
    EntireLine,
}

/// Processed text with ANSI formatting information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormattedText {
    pub segments: Vec<TextSegment>,
}

/// A segment of text with consistent formatting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextSegment {
    pub text: String,
    pub foreground: Option<Color>,
    pub background: Option<Color>,
    pub styles: Vec<TextStyle>,
}

impl FormattedText {
    /// Create new empty formatted text
    pub fn new() -> Self {
        Self {
            segments: Vec::new(),
        }
    }

    /// Add a text segment
    pub fn add_segment(&mut self, segment: TextSegment) {
        self.segments.push(segment);
    }

    /// Get plain text without formatting
    pub fn to_plain_text(&self) -> String {
        self.segments.iter().map(|s| s.text.as_str()).collect::<String>()
    }

    /// Get total length of text
    pub fn len(&self) -> usize {
        self.segments.iter().map(|s| s.text.len()).sum()
    }

    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.segments.is_empty() || self.segments.iter().all(|s| s.text.is_empty())
    }
}

impl Default for FormattedText {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for FormattedText {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        for segment in &self.segments {
            write!(f, "{}", segment.text)?;
        }
        Ok(())
    }
}

impl TextSegment {
    /// Create a new text segment with default formatting
    pub fn new(text: String) -> Self {
        Self {
            text,
            foreground: None,
            background: None,
            styles: Vec::new(),
        }
    }

    /// Create a text segment with specific formatting
    pub fn with_formatting(
        text: String,
        foreground: Option<Color>,
        background: Option<Color>,
        styles: Vec<TextStyle>,
    ) -> Self {
        Self {
            text,
            foreground,
            background,
            styles,
        }
    }

    /// Check if segment has any formatting
    pub fn has_formatting(&self) -> bool {
        self.foreground.is_some() || self.background.is_some() || !self.styles.is_empty()
    }

    /// Apply a style to this segment
    pub fn with_style(mut self, style: TextStyle) -> Self {
        if !self.styles.contains(&style) {
            self.styles.push(style);
        }
        self
    }

    /// Set foreground color
    pub fn with_foreground(mut self, color: Color) -> Self {
        self.foreground = Some(color);
        self
    }

    /// Set background color
    pub fn with_background(mut self, color: Color) -> Self {
        self.background = Some(color);
        self
    }
}

/// ANSI escape sequence processor
pub struct AnsiProcessor {
    parser: AnsiParser,
    current_state: FormattingState,
}

/// Current formatting state
#[derive(Debug, Clone, Default)]
struct FormattingState {
    foreground: Option<Color>,
    background: Option<Color>,
    styles: Vec<TextStyle>,
}

impl AnsiProcessor {
    /// Create a new ANSI processor
    pub fn new() -> Self {
        Self {
            parser: AnsiParser::new(),
            current_state: FormattingState::default(),
        }
    }

    /// Process text with ANSI escape sequences
    pub fn process(&mut self, text: &str) -> FormattedText {
        let mut result = FormattedText::new();
        let tokens = self.parser.parse(text);

        for token in tokens {
            match token {
                ParsedToken::Text(text) => {
                    if !text.is_empty() {
                        let segment = TextSegment::with_formatting(
                            text,
                            self.current_state.foreground.clone(),
                            self.current_state.background.clone(),
                            self.current_state.styles.clone(),
                        );
                        result.add_segment(segment);
                    }
                }
                ParsedToken::Sequence(seq) => {
                    self.apply_sequence(&seq);
                }
            }
        }

        result
    }

    /// Apply an ANSI sequence to current state
    fn apply_sequence(&mut self, sequence: &AnsiSequence) {
        match sequence {
            AnsiSequence::Reset => {
                self.current_state = FormattingState::default();
            }
            AnsiSequence::ForegroundColor(color) => {
                self.current_state.foreground = Some(color.clone());
            }
            AnsiSequence::BackgroundColor(color) => {
                self.current_state.background = Some(color.clone());
            }
            AnsiSequence::Style(style) => {
                match style {
                    TextStyle::BoldOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Bold);
                    }
                    TextStyle::DimOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Dim);
                    }
                    TextStyle::ItalicOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Italic);
                    }
                    TextStyle::UnderlineOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Underline);
                    }
                    TextStyle::BlinkOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Blink);
                    }
                    TextStyle::ReverseOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Reverse);
                    }
                    TextStyle::StrikethroughOff => {
                        self.current_state.styles.retain(|s| s != &TextStyle::Strikethrough);
                    }
                    _ => {
                        if !self.current_state.styles.contains(style) {
                            self.current_state.styles.push(style.clone());
                        }
                    }
                }
            }
            AnsiSequence::CursorMove(_) | AnsiSequence::Clear(_) => {
                // These don't affect text formatting
            }
            AnsiSequence::Unknown(_) => {
                // Ignore unknown sequences
            }
        }
    }

    /// Reset processor state
    pub fn reset(&mut self) {
        self.current_state = FormattingState::default();
    }
}

impl Default for AnsiProcessor {
    fn default() -> Self {
        Self::new()
    }
}

/// Utility functions for common ANSI operations
pub mod utils {
    use super::*;

    /// Strip all ANSI escape sequences from text
    pub fn strip_ansi(text: &str) -> String {
        let mut processor = AnsiProcessor::new();
        processor.process(text).to_plain_text()
    }

    /// Count visible characters (excluding ANSI sequences)
    pub fn visible_length(text: &str) -> usize {
        strip_ansi(text).len()
    }

    /// Truncate text to visible length, preserving ANSI sequences
    pub fn truncate_visible(text: &str, max_len: usize) -> String {
        let mut processor = AnsiProcessor::new();
        let formatted = processor.process(text);
        
        let mut result = String::new();
        let mut current_len = 0;
        
        for segment in formatted.segments {
            if current_len >= max_len {
                break;
            }
            
            let remaining = max_len - current_len;
            if segment.text.len() <= remaining {
                result.push_str(&segment.text);
                current_len += segment.text.len();
            } else {
                let truncated = segment.text.chars().take(remaining).collect::<String>();
                result.push_str(&truncated);
                break;
            }
        }
        
        result
    }
}
