[package]
name = "arien-core"
version = "0.1.0"
edition = "2024"

[dependencies]
arien-common = { path = "../common" }
arien-mcp-types = { path = "../mcp-types" }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
tokio-util = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }
flume = { workspace = true }
reqwest = { workspace = true }
toml = { workspace = true }
regex = { workspace = true }
base64 = { workspace = true }
image = { workspace = true }
walkdir = { workspace = true }
sha2 = { workspace = true }
dirs = "5.0"
futures = "0.3"
