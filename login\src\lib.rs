use arien_common::{Result, <PERSON>enError};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use chrono::{DateTime, Utc};
use uuid::Uuid;
use sha2::Digest;

/// Authentication and session management for Arien AI
pub mod auth;
pub mod session;
pub mod storage;

pub use auth::*;
pub use session::*;
pub use storage::*;

/// User authentication information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserAuth {
    pub user_id: Uuid,
    pub username: String,
    pub email: Option<String>,
    pub created_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
    pub is_active: bool,
}

/// Authentication token
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthToken {
    pub token: String,
    pub user_id: Uuid,
    pub expires_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

/// Login credentials
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginCredentials {
    pub username: String,
    pub password: String,
}

/// Login response
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginResponse {
    pub success: bool,
    pub token: Option<AuthToken>,
    pub user: Option<UserAuth>,
    pub error: Option<String>,
}

/// Authentication manager
pub struct AuthManager {
    storage: Box<dyn AuthStorage>,
}

impl AuthManager {
    /// Create a new authentication manager
    pub fn new(storage: Box<dyn AuthStorage>) -> Self {
        Self { storage }
    }

    /// Authenticate user with credentials
    pub async fn authenticate(&self, credentials: LoginCredentials) -> Result<LoginResponse> {
        // For now, implement a simple authentication system
        // In production, this would integrate with proper authentication providers
        
        if credentials.username.is_empty() || credentials.password.is_empty() {
            return Ok(LoginResponse {
                success: false,
                token: None,
                user: None,
                error: Some("Username and password are required".to_string()),
            });
        }

        // Check if user exists
        if let Some(user) = self.storage.get_user_by_username(&credentials.username).await? {
            // Verify password (in production, use proper password hashing)
            if self.verify_password(&credentials.password, &user.user_id).await? {
                // Generate token
                let token = self.generate_token(user.user_id).await?;
                
                // Update last login
                self.storage.update_last_login(user.user_id).await?;
                
                return Ok(LoginResponse {
                    success: true,
                    token: Some(token),
                    user: Some(user),
                    error: None,
                });
            }
        }

        Ok(LoginResponse {
            success: false,
            token: None,
            user: None,
            error: Some("Invalid username or password".to_string()),
        })
    }

    /// Verify authentication token
    pub async fn verify_token(&self, token: &str) -> Result<Option<UserAuth>> {
        if let Some(auth_token) = self.storage.get_token(token).await? {
            if auth_token.expires_at > Utc::now() {
                return self.storage.get_user_by_id(auth_token.user_id).await;
            }
        }
        Ok(None)
    }

    /// Create a new user
    pub async fn create_user(&self, username: String, password: String, email: Option<String>) -> Result<UserAuth> {
        let user = UserAuth {
            user_id: Uuid::new_v4(),
            username,
            email,
            created_at: Utc::now(),
            last_login: None,
            is_active: true,
        };

        self.storage.create_user(&user).await?;
        self.storage.set_password(user.user_id, &password).await?;

        Ok(user)
    }

    /// Generate authentication token
    async fn generate_token(&self, user_id: Uuid) -> Result<AuthToken> {
        let token = AuthToken {
            token: Uuid::new_v4().to_string(),
            user_id,
            expires_at: Utc::now() + chrono::Duration::hours(24),
            created_at: Utc::now(),
        };

        self.storage.store_token(&token).await?;
        Ok(token)
    }

    /// Verify password
    async fn verify_password(&self, password: &str, user_id: &Uuid) -> Result<bool> {
        // In production, use proper password hashing (bcrypt, argon2, etc.)
        if let Some(stored_hash) = self.storage.get_password_hash(*user_id).await? {
            // Simple hash comparison for now
            let password_hash = format!("{:x}", sha2::Sha256::digest(password.as_bytes()));
            Ok(password_hash == stored_hash)
        } else {
            Ok(false)
        }
    }

    /// Logout user (invalidate token)
    pub async fn logout(&self, token: &str) -> Result<()> {
        self.storage.invalidate_token(token).await
    }
}

/// Get default authentication data directory
pub fn get_auth_data_dir() -> Result<PathBuf> {
    let mut path = dirs::data_dir()
        .ok_or_else(|| ArienError::Io(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "Could not find data directory"
        )))?;
    
    path.push("arien-ai");
    path.push("auth");
    
    std::fs::create_dir_all(&path)?;
    Ok(path)
}
