use crate::{Use<PERSON><PERSON><PERSON>, AuthToken, Session};
use arien_common::{Result, ArienError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sha2::Digest;

/// Authentication storage trait
#[async_trait::async_trait]
pub trait AuthStorage: Send + Sync {
    /// Create a new user
    async fn create_user(&self, user: &UserAuth) -> Result<()>;
    
    /// Get user by ID
    async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<UserAuth>>;
    
    /// Get user by username
    async fn get_user_by_username(&self, username: &str) -> Result<Option<UserAuth>>;
    
    /// Update user's last login time
    async fn update_last_login(&self, user_id: Uuid) -> Result<()>;
    
    /// Set user password hash
    async fn set_password(&self, user_id: Uuid, password: &str) -> Result<()>;
    
    /// Get user password hash
    async fn get_password_hash(&self, user_id: Uuid) -> Result<Option<String>>;
    
    /// Store authentication token
    async fn store_token(&self, token: &AuthToken) -> Result<()>;
    
    /// Get authentication token
    async fn get_token(&self, token: &str) -> Result<Option<AuthToken>>;
    
    /// Invalidate authentication token
    async fn invalidate_token(&self, token: &str) -> Result<()>;
    
    /// Store session
    async fn store_session(&self, session: &Session) -> Result<()>;
    
    /// Get session by token
    async fn get_session(&self, token: &str) -> Result<Option<Session>>;
    
    /// Update session activity
    async fn update_session_activity(&self, token: &str, last_activity: DateTime<Utc>) -> Result<()>;
    
    /// Invalidate session
    async fn invalidate_session(&self, token: &str) -> Result<()>;
    
    /// Get all sessions for a user
    async fn get_user_sessions(&self, user_id: Uuid) -> Result<Vec<Session>>;
    
    /// Set session metadata
    async fn set_session_metadata(&self, token: &str, key: String, value: String) -> Result<()>;
    
    /// Get session metadata
    async fn get_session_metadata(&self, token: &str, key: &str) -> Result<Option<String>>;
    
    /// Clean up expired sessions and tokens
    async fn cleanup_expired_sessions(&self) -> Result<()>;
}

/// File-based authentication storage
pub struct FileAuthStorage {
    data_dir: PathBuf,
}

impl FileAuthStorage {
    /// Create a new file-based auth storage
    pub fn new(data_dir: PathBuf) -> Self {
        Self { data_dir }
    }

    /// Get users file path
    fn users_file(&self) -> PathBuf {
        self.data_dir.join("users.json")
    }

    /// Get passwords file path
    fn passwords_file(&self) -> PathBuf {
        self.data_dir.join("passwords.json")
    }

    /// Get tokens file path
    fn tokens_file(&self) -> PathBuf {
        self.data_dir.join("tokens.json")
    }

    /// Get sessions file path
    fn sessions_file(&self) -> PathBuf {
        self.data_dir.join("sessions.json")
    }

    /// Load users from file
    async fn load_users(&self) -> Result<HashMap<Uuid, UserAuth>> {
        let file_path = self.users_file();
        if !file_path.exists() {
            return Ok(HashMap::new());
        }

        let content = tokio::fs::read_to_string(file_path).await?;
        let users: HashMap<Uuid, UserAuth> = serde_json::from_str(&content)
            .map_err(|e| ArienError::Io(std::io::Error::new(std::io::ErrorKind::InvalidData, e)))?;
        
        Ok(users)
    }

    /// Save users to file
    async fn save_users(&self, users: &HashMap<Uuid, UserAuth>) -> Result<()> {
        std::fs::create_dir_all(&self.data_dir)?;
        let content = serde_json::to_string_pretty(users)?;
        tokio::fs::write(self.users_file(), content).await?;
        Ok(())
    }

    /// Load passwords from file
    async fn load_passwords(&self) -> Result<HashMap<Uuid, String>> {
        let file_path = self.passwords_file();
        if !file_path.exists() {
            return Ok(HashMap::new());
        }

        let content = tokio::fs::read_to_string(file_path).await?;
        let passwords: HashMap<Uuid, String> = serde_json::from_str(&content)
            .map_err(|e| ArienError::Io(std::io::Error::new(std::io::ErrorKind::InvalidData, e)))?;
        
        Ok(passwords)
    }

    /// Save passwords to file
    async fn save_passwords(&self, passwords: &HashMap<Uuid, String>) -> Result<()> {
        std::fs::create_dir_all(&self.data_dir)?;
        let content = serde_json::to_string_pretty(passwords)?;
        tokio::fs::write(self.passwords_file(), content).await?;
        Ok(())
    }

    /// Load tokens from file
    async fn load_tokens(&self) -> Result<HashMap<String, AuthToken>> {
        let file_path = self.tokens_file();
        if !file_path.exists() {
            return Ok(HashMap::new());
        }

        let content = tokio::fs::read_to_string(file_path).await?;
        let tokens: HashMap<String, AuthToken> = serde_json::from_str(&content)
            .map_err(|e| ArienError::Io(std::io::Error::new(std::io::ErrorKind::InvalidData, e)))?;
        
        Ok(tokens)
    }

    /// Save tokens to file
    async fn save_tokens(&self, tokens: &HashMap<String, AuthToken>) -> Result<()> {
        std::fs::create_dir_all(&self.data_dir)?;
        let content = serde_json::to_string_pretty(tokens)?;
        tokio::fs::write(self.tokens_file(), content).await?;
        Ok(())
    }

    /// Load sessions from file
    async fn load_sessions(&self) -> Result<HashMap<String, Session>> {
        let file_path = self.sessions_file();
        if !file_path.exists() {
            return Ok(HashMap::new());
        }

        let content = tokio::fs::read_to_string(file_path).await?;
        let sessions: HashMap<String, Session> = serde_json::from_str(&content)
            .map_err(|e| ArienError::Io(std::io::Error::new(std::io::ErrorKind::InvalidData, e)))?;
        
        Ok(sessions)
    }

    /// Save sessions to file
    async fn save_sessions(&self, sessions: &HashMap<String, Session>) -> Result<()> {
        std::fs::create_dir_all(&self.data_dir)?;
        let content = serde_json::to_string_pretty(sessions)?;
        tokio::fs::write(self.sessions_file(), content).await?;
        Ok(())
    }
}

#[async_trait::async_trait]
impl AuthStorage for FileAuthStorage {
    async fn create_user(&self, user: &UserAuth) -> Result<()> {
        let mut users = self.load_users().await?;
        users.insert(user.user_id, user.clone());
        self.save_users(&users).await?;
        Ok(())
    }

    async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<UserAuth>> {
        let users = self.load_users().await?;
        Ok(users.get(&user_id).cloned())
    }

    async fn get_user_by_username(&self, username: &str) -> Result<Option<UserAuth>> {
        let users = self.load_users().await?;
        Ok(users.values().find(|u| u.username == username).cloned())
    }

    async fn update_last_login(&self, user_id: Uuid) -> Result<()> {
        let mut users = self.load_users().await?;
        if let Some(user) = users.get_mut(&user_id) {
            user.last_login = Some(Utc::now());
            self.save_users(&users).await?;
        }
        Ok(())
    }

    async fn set_password(&self, user_id: Uuid, password: &str) -> Result<()> {
        let mut passwords = self.load_passwords().await?;
        // Hash password with SHA-256 (use bcrypt in production)
        let password_hash = format!("{:x}", sha2::Sha256::digest(password.as_bytes()));
        passwords.insert(user_id, password_hash);
        self.save_passwords(&passwords).await?;
        Ok(())
    }

    async fn get_password_hash(&self, user_id: Uuid) -> Result<Option<String>> {
        let passwords = self.load_passwords().await?;
        Ok(passwords.get(&user_id).cloned())
    }

    async fn store_token(&self, token: &AuthToken) -> Result<()> {
        let mut tokens = self.load_tokens().await?;
        tokens.insert(token.token.clone(), token.clone());
        self.save_tokens(&tokens).await?;
        Ok(())
    }

    async fn get_token(&self, token: &str) -> Result<Option<AuthToken>> {
        let tokens = self.load_tokens().await?;
        Ok(tokens.get(token).cloned())
    }

    async fn invalidate_token(&self, token: &str) -> Result<()> {
        let mut tokens = self.load_tokens().await?;
        tokens.remove(token);
        self.save_tokens(&tokens).await?;
        Ok(())
    }

    async fn store_session(&self, session: &Session) -> Result<()> {
        let mut sessions = self.load_sessions().await?;
        sessions.insert(session.token.clone(), session.clone());
        self.save_sessions(&sessions).await?;
        Ok(())
    }

    async fn get_session(&self, token: &str) -> Result<Option<Session>> {
        let sessions = self.load_sessions().await?;
        Ok(sessions.get(token).cloned())
    }

    async fn update_session_activity(&self, token: &str, last_activity: DateTime<Utc>) -> Result<()> {
        let mut sessions = self.load_sessions().await?;
        if let Some(session) = sessions.get_mut(token) {
            session.last_activity = last_activity;
            self.save_sessions(&sessions).await?;
        }
        Ok(())
    }

    async fn invalidate_session(&self, token: &str) -> Result<()> {
        let mut sessions = self.load_sessions().await?;
        if let Some(session) = sessions.get_mut(token) {
            session.is_active = false;
        }
        self.save_sessions(&sessions).await?;
        Ok(())
    }

    async fn get_user_sessions(&self, user_id: Uuid) -> Result<Vec<Session>> {
        let sessions = self.load_sessions().await?;
        Ok(sessions.values()
            .filter(|s| s.user_id == user_id && s.is_active)
            .cloned()
            .collect())
    }

    async fn set_session_metadata(&self, token: &str, key: String, value: String) -> Result<()> {
        let mut sessions = self.load_sessions().await?;
        if let Some(session) = sessions.get_mut(token) {
            session.metadata.insert(key, value);
            self.save_sessions(&sessions).await?;
        }
        Ok(())
    }

    async fn get_session_metadata(&self, token: &str, key: &str) -> Result<Option<String>> {
        let sessions = self.load_sessions().await?;
        Ok(sessions.get(token)
            .and_then(|s| s.metadata.get(key))
            .cloned())
    }

    async fn cleanup_expired_sessions(&self) -> Result<()> {
        let now = Utc::now();
        let mut sessions = self.load_sessions().await?;
        let mut tokens = self.load_tokens().await?;

        // Remove expired sessions
        sessions.retain(|_, session| session.is_active && session.expires_at > now);
        
        // Remove expired tokens
        tokens.retain(|_, token| token.expires_at > now);

        self.save_sessions(&sessions).await?;
        self.save_tokens(&tokens).await?;

        Ok(())
    }
}
