{"models": {"main": {"provider": "deepseek", "modelId": "deepseek-reasoner", "maxTokens": 120000, "temperature": 0.7}, "research": {"provider": "deepseek", "modelId": "deepseek-reasoner", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "anthropic", "modelId": "claude-3-7-sonnet-20250219", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/"}}